-- Budget Management Functions
-- These functions handle budget item management, snapshots, and reversion
-- Function to calculate the cost of a budget line item
CREATE OR REPLACE FUNCTION public.calculate_unit_item_cost (
	p_material_rate NUMERIC,
	p_labor_rate NUMERIC,
	p_productivity_per_hour NUMERIC
) RETURNS NUMERIC
SET
	search_path = '' AS $$
DECLARE v_cost NUMERIC;
BEGIN -- Simple calculation for now - can be enhanced later
v_cost := COALESCE(p_material_rate, 0);
-- Add labor costs if applicable
IF p_labor_rate IS NOT NULL
AND p_productivity_per_hour IS NOT NULL
AND p_productivity_per_hour > 0 THEN v_cost := v_cost + COALESCE(p_labor_rate, 0) / COALESCE(p_productivity_per_hour, 1);
END IF;
RETURN v_cost;
END;
$$ LANGUAGE plpgsql;

-- Function to create or update a budget line item
-- Note: A WBS item can have multiple budget line items associated with it
CREATE OR REPLACE FUNCTION public.upsert_budget_line_item (
	p_project_id UUID,
	p_wbs_library_item_id UUID,
	p_quantity NUMERIC,
	-- Optional parameters for the function
	p_unit TEXT DEFAULT NULL,
	p_material_rate NUMERIC default 0,
	p_labor_rate NUMERIC DEFAULT NULL,
	p_productivity_per_hour NUMERIC DEFAULT NULL,
	p_unit_rate_manual_override BOOLEAN DEFAULT FALSE,
	p_unit_rate NUMERIC DEFAULT 0,
	p_factor NUMERIC DEFAULT NULL,
	p_remarks TEXT DEFAULT NULL,
	p_cost_certainty NUMERIC DEFAULT NULL,
	p_design_certainty NUMERIC DEFAULT NULL,
	p_change_reason TEXT DEFAULT NULL,
	p_budget_line_item_id BIGINT DEFAULT NULL
) RETURNS BIGINT
SET
	search_path = '' AS $$
DECLARE v_calculated_cost NUMERIC;
v_cost_to_use NUMERIC;
v_exists BOOLEAN;
v_budget_line_item_id BIGINT;
BEGIN -- Calculate the cost (always calculate regardless of override)
v_calculated_cost := public.calculate_unit_item_cost(
	p_material_rate,
	p_labor_rate,
	p_productivity_per_hour
);
-- Determine which cost to use based on override flag
IF COALESCE(p_unit_rate_manual_override, FALSE) = TRUE THEN -- When manual override is enabled, use the provided value or fall back to calculated
v_cost_to_use := COALESCE(p_unit_rate, v_calculated_cost);
ELSE -- When manual override is disabled or NULL, always use the calculated value
v_cost_to_use := v_calculated_cost;
END IF;
-- Check if we are updating an existing line item by ID
IF p_budget_line_item_id IS NOT NULL THEN -- Check if the budget line item exists
SELECT EXISTS (
		SELECT 1
		FROM public.budget_line_item_current
		WHERE budget_line_item_id = p_budget_line_item_id
	) INTO v_exists;
IF v_exists THEN -- Update existing record by ID (trigger will handle audit logging)
UPDATE public.budget_line_item_current
SET quantity = p_quantity,
	unit = p_unit,
	material_rate = p_material_rate,
	labor_rate = p_labor_rate,
	productivity_per_hour = p_productivity_per_hour,
	unit_rate_manual_override = p_unit_rate_manual_override,
	unit_rate = v_cost_to_use,
	factor = p_factor,
	remarks = p_remarks,
	cost_certainty = p_cost_certainty,
	design_certainty = p_design_certainty,
	updated_at = now()
WHERE budget_line_item_id = p_budget_line_item_id
RETURNING budget_line_item_id INTO v_budget_line_item_id;
RETURN v_budget_line_item_id;
END IF;
END IF;
-- Insert new record
INSERT INTO public.budget_line_item_current (
		project_id,
		wbs_library_item_id,
		quantity,
		unit,
		material_rate,
		labor_rate,
		productivity_per_hour,
		unit_rate_manual_override,
		unit_rate,
		factor,
		remarks,
		cost_certainty,
		design_certainty
	)
VALUES (
		p_project_id,
		p_wbs_library_item_id,
		p_quantity,
		p_unit,
		p_material_rate,
		p_labor_rate,
		p_productivity_per_hour,
		p_unit_rate_manual_override,
		v_cost_to_use,
		p_factor,
		p_remarks,
		p_cost_certainty,
		p_design_certainty
	)
RETURNING budget_line_item_id INTO v_budget_line_item_id;
-- For new items, manually create an audit entry since the trigger only fires on UPDATE
INSERT INTO public.budget_line_item_audit (
		project_id,
		wbs_library_item_id,
		changed_by_user_id,
		change_timestamp,
		change_reason,
		old_quantity,
		old_unit,
		old_material_rate,
		old_labor_rate,
		old_productivity_per_hour,
		old_unit_rate_manual_override,
		old_unit_rate,
		old_factor,
		old_remarks,
		old_cost_certainty,
		old_design_certainty,
		new_quantity,
		new_unit,
		new_material_rate,
		new_labor_rate,
		new_productivity_per_hour,
		new_unit_rate_manual_override,
		new_unit_rate,
		new_factor,
		new_remarks,
		new_cost_certainty,
		new_design_certainty
	)
VALUES (
		p_project_id,
		p_wbs_library_item_id,
		auth.uid(),
		now(),
		COALESCE(p_change_reason, 'New item created'),
		NULL,
		NULL,
		NULL,
		NULL,
		NULL,
		NULL,
		NULL,
		NULL,
		NULL,
		NULL,
		NULL,
		p_quantity,
		p_unit,
		p_material_rate,
		p_labor_rate,
		p_productivity_per_hour,
		p_unit_rate_manual_override,
		v_cost_to_use,
		p_factor,
		p_remarks,
		p_cost_certainty,
		p_design_certainty
	);
RETURN v_budget_line_item_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create a budget snapshot for a project stage
CREATE OR REPLACE FUNCTION public.create_budget_snapshot (
	p_project_stage_id BIGINT,
	p_freeze_reason TEXT DEFAULT NULL
) RETURNS BIGINT
SET
	search_path = '' AS $$
DECLARE v_project_id UUID;
v_snapshot_id BIGINT;
v_item RECORD;
BEGIN -- Get the project_id from the stage
SELECT project_id INTO v_project_id
FROM public.project_stage
WHERE project_stage_id = p_project_stage_id;
IF v_project_id IS NULL THEN RAISE EXCEPTION 'Project stage not found';
END IF;
-- Create the snapshot
INSERT INTO public.budget_snapshot (
		project_stage_id,
		freeze_date,
		freeze_reason,
		created_by_user_id
	)
VALUES (
		p_project_stage_id,
		now(),
		p_freeze_reason,
		auth.uid()
	)
RETURNING budget_snapshot_id INTO v_snapshot_id;
-- Copy all current budget line items for this project to the snapshot
FOR v_item IN (
	SELECT *
	FROM public.budget_line_item_current
	WHERE project_id = v_project_id
) LOOP
INSERT INTO public.budget_snapshot_line_item (
		budget_snapshot_id,
		wbs_library_item_id,
		quantity,
		unit,
		material_rate,
		labor_rate,
		productivity_per_hour,
		unit_rate_manual_override,
		unit_rate,
		factor,
		remarks,
		cost_certainty,
		design_certainty
	)
VALUES (
		v_snapshot_id,
		v_item.wbs_library_item_id,
		v_item.quantity,
		v_item.unit,
		v_item.material_rate,
		v_item.labor_rate,
		v_item.productivity_per_hour,
		v_item.unit_rate_manual_override,
		v_item.unit_rate,
		v_item.factor,
		v_item.remarks,
		v_item.cost_certainty,
		v_item.design_certainty
	);
END LOOP;
-- Update the project stage to mark it as completed
UPDATE public.project_stage
SET date_completed = now()
WHERE project_stage_id = p_project_stage_id;
RETURN v_snapshot_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to revert budget to a snapshot state
CREATE OR REPLACE FUNCTION public.revert_to_budget_snapshot (
	p_budget_snapshot_id BIGINT,
	p_revert_reason TEXT DEFAULT 'Reverted to snapshot'
) RETURNS BOOLEAN
SET
	search_path = '' AS $$
DECLARE v_project_id UUID;
v_item RECORD;
BEGIN -- Get the project_id from the snapshot and stage
SELECT project_id INTO v_project_id
FROM public.project_stage ps
	JOIN public.budget_snapshot bs ON ps.project_stage_id = bs.project_stage_id
WHERE bs.budget_snapshot_id = p_budget_snapshot_id;
IF v_project_id IS NULL THEN RAISE EXCEPTION 'Budget snapshot not found or not linked to a valid project';
END IF;
-- Loop through all snapshot items and update/insert into current budget
FOR v_item IN (
	SELECT *
	FROM public.budget_snapshot_line_item
	WHERE budget_snapshot_id = p_budget_snapshot_id
) LOOP -- Use the upsert function for each item
PERFORM public.upsert_budget_line_item(
	v_project_id,
	v_item.wbs_library_item_id,
	v_item.quantity,
	v_item.unit,
	v_item.material_rate,
	v_item.labor_rate,
	v_item.productivity_per_hour,
	v_item.unit_rate_manual_override,
	v_item.unit_rate,
	v_item.factor,
	v_item.remarks,
	v_item.cost_certainty,
	v_item.design_certainty,
	p_revert_reason,
	NULL -- We want to create new records when reverting, not update existing ones
);
END LOOP;
RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to compare snapshots
CREATE OR REPLACE FUNCTION public.compare_budget_snapshots (p_snapshot_id_1 BIGINT, p_snapshot_id_2 BIGINT) RETURNS TABLE (
	wbs_library_item_id UUID,
	snapshot_1_quantity NUMERIC,
	snapshot_1_cost NUMERIC,
	snapshot_2_quantity NUMERIC,
	snapshot_2_cost NUMERIC,
	quantity_diff NUMERIC,
	cost_diff NUMERIC,
	percent_change NUMERIC
)
SET
	search_path = '' AS $$ BEGIN RETURN QUERY
SELECT COALESCE(s1.wbs_library_item_id, s2.wbs_library_item_id) AS wbs_library_item_id,
	s1.quantity AS snapshot_1_quantity,
	s1.unit_rate AS snapshot_1_cost,
	s1.factor AS snapshot_1_factor,
	s2.quantity AS snapshot_2_quantity,
	s2.unit_rate AS snapshot_2_cost,
	s2.factor AS snapshot_2_factor,
	COALESCE(s2.quantity, 0) - COALESCE(s1.quantity, 0) AS quantity_diff,
	COALESCE(s2.unit_rate, 0) - COALESCE(s1.unit_rate, 0) AS cost_diff,
	CASE
		WHEN COALESCE(s1.unit_rate, 0) = 0 THEN CASE
			WHEN COALESCE(s2.unit_rate, 0) = 0 THEN 0
			ELSE NULL -- Can't calculate percent change from zero
		END
		ELSE (
			COALESCE(s2.unit_rate, 0) - COALESCE(s1.unit_rate, 0)
		) / COALESCE(s1.unit_rate, 1) * COALESCE(s1.factor, 1) / COALESCE(s2.factor, 1) * 100
	END AS percent_change
FROM (
		SELECT *
		FROM public.budget_snapshot_line_item
		WHERE budget_snapshot_id = p_snapshot_id_1
	) s1
	FULL OUTER JOIN (
		SELECT *
		FROM public.budget_snapshot_line_item
		WHERE budget_snapshot_id = p_snapshot_id_2
	) s2 ON s1.wbs_library_item_id = s2.wbs_library_item_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate if a stage is ready for completion
-- Checks if all gateway checklist items for a stage have a status of 'Complete'
-- Uses the gateway_checklist_item_status_log table to get the latest status for each item
CREATE OR REPLACE FUNCTION public.is_stage_ready_for_completion (p_project_stage_id BIGINT) RETURNS BOOLEAN
SET
	search_path = '' AS $$
DECLARE
    v_checklist_count INTEGER;
    v_completed_count INTEGER;
BEGIN
    -- Count total checklist items and completed items
    -- Only consider the most recent status for each item (where latest = true)
    SELECT
        COUNT(*),
        COUNT(*) FILTER (
            WHERE status = 'Complete'
        )
    INTO
        v_checklist_count,
        v_completed_count
    FROM
        public.gateway_checklist_item gci
    JOIN
        public.gateway_checklist_item_status_log gcisl
        ON gci.gateway_checklist_item_id = gcisl.gateway_checklist_item_id
    WHERE
        gci.project_stage_id = p_project_stage_id
        AND gcisl.latest = TRUE;

    -- If there are no checklist items, stage is ready for completion
    IF v_checklist_count = 0 THEN
        RETURN TRUE;
    END IF;

    -- Stage is ready if all checklist items have a status of 'Complete'
    RETURN v_checklist_count = v_completed_count;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error and return false to prevent stage completion
        RAISE WARNING 'Error checking if stage is ready for completion: %', SQLERRM;
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to complete a stage with snapshot
CREATE OR REPLACE FUNCTION public.complete_project_stage (
	p_project_stage_id BIGINT,
	p_completion_notes TEXT DEFAULT NULL
) RETURNS BIGINT
SET
	search_path = '' AS $$
DECLARE v_snapshot_id BIGINT;
v_is_ready BOOLEAN;
BEGIN -- Check if all checklist items are completed
SELECT public.is_stage_ready_for_completion(p_project_stage_id) INTO v_is_ready;
IF NOT v_is_ready THEN RAISE EXCEPTION 'Cannot complete stage: not all checklist items are completed';
END IF;
-- Create a budget snapshot
SELECT public.create_budget_snapshot(p_project_stage_id, p_completion_notes) INTO v_snapshot_id;
RETURN v_snapshot_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant access to service_role and authenticated users
GRANT
EXECUTE ON FUNCTION public.calculate_unit_item_cost TO service_role,
authenticated;

GRANT
EXECUTE ON FUNCTION public.upsert_budget_line_item TO service_role,
authenticated;

GRANT
EXECUTE ON FUNCTION public.create_budget_snapshot TO service_role,
authenticated;

GRANT
EXECUTE ON FUNCTION public.revert_to_budget_snapshot TO service_role,
authenticated;

GRANT
EXECUTE ON FUNCTION public.compare_budget_snapshots TO service_role,
authenticated;

GRANT
EXECUTE ON FUNCTION public.is_stage_ready_for_completion TO service_role,
authenticated;

GRANT
EXECUTE ON FUNCTION public.complete_project_stage TO service_role,
authenticated;

-- Function to generate demo budget data for development/testing
-- This replaces the random_budget_v3.sql seed script to work with auth.uid()
CREATE OR REPLACE FUNCTION public.generate_demo_budget_data () RETURNS jsonb
SET
	search_path = '' AS $$
DECLARE
    v_project_id UUID := '11111111-1111-1111-1111-111111111111';
    v_client_id UUID;
    v_user_id UUID := auth.uid();
    stage1_id BIGINT;
    stage2_id BIGINT;
    stage3_id BIGINT;
    stage4_id BIGINT;
    snap1_id BIGINT;
    snap2_id BIGINT;
    snap3_id BIGINT;
    snap4_id BIGINT;
    item2 RECORD;
    item3 RECORD;
    item4 RECORD;
    stage_total NUMERIC;
    child_count INT;
    base_share NUMERIC;
    portion NUMERIC;
    remaining NUMERIC;
    idx INT;
    v_existing_project_count INT;
BEGIN
    -- Check if user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to generate demo budget data';
    END IF;

    -- Check if demo project already exists
    SELECT COUNT(*) INTO v_existing_project_count
    FROM public.project
    WHERE project_id = v_project_id;

    IF v_existing_project_count > 0 THEN
        RAISE EXCEPTION 'Demo project already exists. Please delete it first before regenerating.';
    END IF;

    -- Look up client inserted in data.sql
    SELECT client_id INTO v_client_id FROM public.client WHERE name='Unity' LIMIT 1;

    IF v_client_id IS NULL THEN
        RAISE EXCEPTION 'Unity client not found. Please ensure seed data is loaded.';
    END IF;

    -- Create project
    INSERT INTO public.project (project_id, name, description, client_id, wbs_library_id, created_by_user_id)
    VALUES (v_project_id, 'ICMS Test Project', 'Seeded test project', v_client_id, 1, v_user_id);

    -- Create four stages
    INSERT INTO public.project_stage (project_id, name, stage_order, stage)
    VALUES (v_project_id, 'Stage 1', 1, 1)
    RETURNING project_stage_id INTO stage1_id;

    INSERT INTO public.project_stage (project_id, name, stage_order, stage)
    VALUES (v_project_id, 'Stage 2', 2, 2)
    RETURNING project_stage_id INTO stage2_id;

    INSERT INTO public.project_stage (project_id, name, stage_order, stage)
    VALUES (v_project_id, 'Stage 3', 3, 3)
    RETURNING project_stage_id INTO stage3_id;

    INSERT INTO public.project_stage (project_id, name, stage_order, stage)
    VALUES (v_project_id, 'Stage 4', 4, 4)
    RETURNING project_stage_id INTO stage4_id;

    -- Stage 1 budget across level 2 items
    FOR item2 IN
        SELECT wbs_library_item_id FROM public.wbs_library_item WHERE wbs_library_id = 1 AND level = 2
    LOOP
        portion := ROUND((10000 + RANDOM() * 90000)::NUMERIC, 2);
        INSERT INTO public.budget_line_item_current (
            project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override
        ) VALUES (
            v_project_id, item2.wbs_library_item_id, 1, portion, portion, FALSE
        );
    END LOOP;

    -- Snapshot after stage 1
    SELECT public.create_budget_snapshot(stage1_id, 'Stage 1 budget') INTO snap1_id;

    -- Prepare for stage 2
    DELETE FROM public.budget_line_item_current WHERE project_id = v_project_id;

    FOR item2 IN
        SELECT wbs_library_item_id, unit_rate FROM public.budget_snapshot_line_item WHERE budget_snapshot_id = snap1_id
    LOOP
        stage_total := item2.unit_rate * (1 + (RANDOM() * 0.5 - 0.25));
        child_count := (SELECT COUNT(*) FROM public.wbs_library_item WHERE parent_item_id = item2.wbs_library_item_id AND level = 3);
        IF child_count = 0 THEN
            INSERT INTO public.budget_line_item_current (project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override)
            VALUES (v_project_id, item2.wbs_library_item_id, 1, ROUND(stage_total, 2), ROUND(stage_total, 2), FALSE);
        ELSE
            base_share := stage_total / child_count;
            remaining := stage_total;
            idx := 0;
            FOR item3 IN
                SELECT wbs_library_item_id FROM public.wbs_library_item WHERE parent_item_id = item2.wbs_library_item_id AND level = 3
            LOOP
                idx := idx + 1;
                IF idx < child_count THEN
                    portion := ROUND(base_share * (0.75 + RANDOM() * 0.5), 2);
                    remaining := remaining - portion;
                ELSE
                    portion := ROUND(remaining, 2);
                END IF;
                INSERT INTO public.budget_line_item_current (project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override)
                VALUES (v_project_id, item3.wbs_library_item_id, 1, portion, portion, FALSE);
            END LOOP;
        END IF;
    END LOOP;

    SELECT public.create_budget_snapshot(stage2_id, 'Stage 2 budget') INTO snap2_id;

    -- Stage 3: spread to level 4 with +/-15%
    DELETE FROM public.budget_line_item_current WHERE project_id = v_project_id;

    FOR item3 IN
        SELECT wbs_library_item_id, unit_rate FROM public.budget_snapshot_line_item WHERE budget_snapshot_id = snap2_id
    LOOP
        stage_total := item3.unit_rate * (1 + (RANDOM() * 0.3 - 0.15));
        child_count := (SELECT COUNT(*) FROM public.wbs_library_item WHERE parent_item_id = item3.wbs_library_item_id AND level = 4);
        IF child_count = 0 THEN
            INSERT INTO public.budget_line_item_current (project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override)
            VALUES (v_project_id, item3.wbs_library_item_id, 1, ROUND(stage_total, 2), ROUND(stage_total, 2), FALSE);
        ELSE
            base_share := stage_total / child_count;
            remaining := stage_total;
            idx := 0;
            FOR item4 IN
                SELECT wbs_library_item_id FROM public.wbs_library_item WHERE parent_item_id = item3.wbs_library_item_id AND level = 4
            LOOP
                idx := idx + 1;
                IF idx < child_count THEN
                    portion := ROUND(base_share * (0.85 + RANDOM() * 0.3), 2);
                    remaining := remaining - portion;
                ELSE
                    portion := ROUND(remaining, 2);
                END IF;
                INSERT INTO public.budget_line_item_current (project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override)
                VALUES (v_project_id, item4.wbs_library_item_id, 1, portion, portion, FALSE);
            END LOOP;
        END IF;
    END LOOP;

    SELECT public.create_budget_snapshot(stage3_id, 'Stage 3 budget') INTO snap3_id;

    -- Stage 4: adjust level 4 values +/-5%
    DELETE FROM public.budget_line_item_current WHERE project_id = v_project_id;

    FOR item4 IN
        SELECT wbs_library_item_id, unit_rate FROM public.budget_snapshot_line_item WHERE budget_snapshot_id = snap3_id
    LOOP
        portion := ROUND(item4.unit_rate * (1 + (RANDOM() * 0.1 - 0.05)), 2);
        INSERT INTO public.budget_line_item_current (project_id, wbs_library_item_id, quantity, material_rate, unit_rate, unit_rate_manual_override)
        VALUES (v_project_id, item4.wbs_library_item_id, 1, portion, portion, FALSE);
    END LOOP;

    SELECT public.create_budget_snapshot(stage4_id, 'Stage 4 budget') INTO snap4_id;

    -- Return success information
    RETURN jsonb_build_object(
        'success', true,
        'message', 'Demo budget data generated successfully',
        'project_id', v_project_id,
        'snapshots', jsonb_build_object(
            'stage1', snap1_id,
            'stage2', snap2_id,
            'stage3', snap3_id,
            'stage4', snap4_id
        )
    );

EXCEPTION
    WHEN OTHERS THEN
        -- Return error information
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'message', 'Failed to generate demo budget data: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant access to authenticated users only
GRANT
EXECUTE ON FUNCTION public.generate_demo_budget_data TO authenticated;
